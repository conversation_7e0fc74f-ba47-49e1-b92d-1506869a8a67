<!DOCTYPE html>
<html>
<head>
    <title>Klaviyo Debug - UPSTEP</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Klaviyo Integration Debug</h1>
    
    <div class="debug-section">
        <h2>1. Theme Settings Check</h2>
        <div id="theme-settings"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. Klaviyo SDK Test</h2>
        <div id="sdk-status"></div>
        <button onclick="testKlaviyoSDK()">Test SDK</button>
    </div>
    
    <div class="debug-section">
        <h2>3. SDK Subscription Test</h2>
        <div id="sdk-subscription-status"></div>
        <input type="email" id="sdk-test-email" placeholder="<EMAIL>" value="<EMAIL>">
        <button onclick="testSDKSubscription()">Test SDK Subscription</button>
    </div>

    <div class="debug-section">
        <h2>4. Direct API Test</h2>
        <div id="api-status"></div>
        <input type="email" id="test-email" placeholder="<EMAIL>" value="<EMAIL>">
        <button onclick="testDirectAPI()">Test Direct API</button>
    </div>
    
    <div class="debug-section">
        <h2>5. Console Logs</h2>
        <div id="console-logs"></div>
    </div>

    <script>
        // Configuration - REPLACE WITH YOUR ACTUAL VALUES
        const CONFIG = {
            klaviyoCompanyId: 'KCJ3Lb', // Company ID for SDK
            klaviyoApiKey: 'KCJ3Lb', // pk_xxxxx for API calls
            listId: 'UKzcec',
            formId: 'Xx9D8d'
        };
        
        let logs = [];
        
        // Override console.log to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            logs.push({type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString()});
            updateConsoleLogs();
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logs.push({type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString()});
            updateConsoleLogs();
            originalError.apply(console, args);
        };
        
        function updateConsoleLogs() {
            const container = document.getElementById('console-logs');
            container.innerHTML = logs.map(log => 
                `<div class="${log.type}"><strong>${log.time}:</strong> ${log.message}</div>`
            ).join('');
        }
        
        function checkThemeSettings() {
            const settings = document.getElementById('theme-settings');
            let html = '<h3>Current Configuration:</h3>';
            
            html += `<p><strong>Company ID:</strong> ${CONFIG.klaviyoCompanyId || '<span class="error">NOT SET</span>'}</p>`;
            html += `<p><strong>Klaviyo API Key:</strong> ${CONFIG.klaviyoApiKey || '<span class="error">NOT SET</span>'}</p>`;
            html += `<p><strong>List ID:</strong> ${CONFIG.listId || '<span class="error">NOT SET</span>'}</p>`;
            html += `<p><strong>Form ID:</strong> ${CONFIG.formId || '<span class="error">NOT SET</span>'}</p>`;
            
            if (!CONFIG.klaviyoCompanyId) {
                html += '<div class="error">⚠️ Company ID is missing!</div>';
            }
            if (!CONFIG.klaviyoApiKey || CONFIG.klaviyoApiKey === 'YOUR_KLAVIYO_PUBLIC_API_KEY') {
                html += '<div class="warning">⚠️ Public API Key needed for direct API calls!</div>';
            }
            
            settings.innerHTML = html;
        }
        
        function testKlaviyoSDK() {
            const status = document.getElementById('sdk-status');
            status.innerHTML = '<p>Loading Klaviyo SDK...</p>';
            
            if (typeof window.klaviyo !== 'undefined') {
                status.innerHTML = '<p class="success">✅ Klaviyo SDK already loaded</p>';
                return;
            }
            
            if (!CONFIG.klaviyoCompanyId) {
                status.innerHTML = '<p class="error">❌ Cannot load SDK: Company ID not configured</p>';
                return;
            }

            const script = document.createElement('script');
            script.src = `https://static.klaviyo.com/onsite/js/klaviyo.js?company_id=${CONFIG.klaviyoCompanyId}`;
            script.async = true;
            
            script.onload = function() {
                status.innerHTML = '<p class="success">✅ Klaviyo SDK loaded successfully</p>';
                console.log('Klaviyo SDK loaded');
            };
            
            script.onerror = function() {
                status.innerHTML = '<p class="error">❌ Failed to load Klaviyo SDK</p>';
                console.error('Failed to load Klaviyo SDK');
            };
            
            document.head.appendChild(script);
        }

        function testSDKSubscription() {
            const status = document.getElementById('sdk-subscription-status');
            const email = document.getElementById('sdk-test-email').value;

            if (!email) {
                status.innerHTML = '<p class="error">Please enter an email address</p>';
                return;
            }

            if (typeof window.klaviyo === 'undefined') {
                status.innerHTML = '<p class="error">❌ Klaviyo SDK not loaded. Try "Test SDK" first.</p>';
                return;
            }

            status.innerHTML = '<p>Testing SDK subscription...</p>';
            console.log('Testing Klaviyo SDK subscription for:', email);

            try {
                // Method 1: Identify user
                window.klaviyo.push(['identify', {
                    '$email': email,
                    'source': 'DEBUG_SDK_TEST',
                    'signup_timestamp': new Date().toISOString()
                }]);

                console.log('✅ Klaviyo identify call made');

                // Method 2: Track subscription event
                window.klaviyo.push(['track', 'Newsletter Signup', {
                    'Email': email,
                    'Source': 'DEBUG_SDK_TEST',
                    'List ID': CONFIG.listId
                }]);

                console.log('✅ Klaviyo track call made');

                status.innerHTML = '<p class="success">✅ SDK calls completed! Check Klaviyo dashboard for results.</p>';

            } catch (error) {
                console.error('❌ SDK Error:', error);
                status.innerHTML = '<p class="error">❌ SDK error: ' + error.message + '</p>';
            }
        }

        function testDirectAPI() {
            const status = document.getElementById('api-status');
            const email = document.getElementById('test-email').value;
            
            if (!email) {
                status.innerHTML = '<p class="error">Please enter an email address</p>';
                return;
            }
            
            if (!CONFIG.klaviyoApiKey || CONFIG.klaviyoApiKey === 'YOUR_KLAVIYO_PUBLIC_API_KEY') {
                status.innerHTML = '<p class="error">❌ Cannot test API: API key not configured</p>';
                return;
            }
            
            status.innerHTML = '<p>Testing direct API call...</p>';
            
            const profileData = {
                data: {
                    type: 'profile',
                    attributes: {
                        email: email,
                        properties: {
                            source: 'DEBUG_TEST',
                            signup_timestamp: new Date().toISOString()
                        }
                    }
                }
            };
            
            console.log('Making API call to Klaviyo...');
            
            fetch(`https://a.klaviyo.com/api/profile-subscription-bulk-create-jobs/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Klaviyo-API-Key ${CONFIG.klaviyoApiKey}`,
                    'Content-Type': 'application/json',
                    'revision': '2024-10-15'
                },
                body: JSON.stringify({
                    data: {
                        type: 'profile-subscription-bulk-create-job',
                        attributes: {
                            profiles: {
                                data: [profileData]
                            },
                            subscriptions: {
                                data: [{
                                    type: 'profile-subscription-bulk-create-job-subscription',
                                    attributes: {
                                        list_id: CONFIG.listId,
                                        channels: {
                                            email: ['MARKETING']
                                        }
                                    }
                                }]
                            }
                        }
                    }
                })
            })
            .then(response => {
                console.log('API Response status:', response.status);
                return response.json().then(data => ({status: response.status, data}));
            })
            .then(({status, data}) => {
                if (status >= 200 && status < 300) {
                    status.innerHTML = '<p class="success">✅ API call successful!</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    status.innerHTML = '<p class="error">❌ API call failed</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
            })
            .catch(error => {
                console.error('API Error:', error);
                status.innerHTML = '<p class="error">❌ API call failed: ' + error.message + '</p>';
            });
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkThemeSettings();
            console.log('Debug page loaded');
        });
    </script>
</body>
</html>
