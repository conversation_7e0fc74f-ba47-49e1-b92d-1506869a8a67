{% comment %}
  Upstep Footer Section - Customizable footer with logo, menus, newsletter, and payment icons
  Features mobile responsiveness and full customization
{% endcomment %}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<footer
  class="
    footer-upstep spacing-style section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}
  "
  style="
    {% render 'spacing-style', settings: section.settings %}
    --footer-bg-color: {{ section.settings.background_color | default: 'rgba(var(--color-background), 1)' }};
    --footer-text-color: {{ section.settings.text_color | default: 'rgba(var(--color-foreground), 1)' }};
    --footer-heading-color: {{ section.settings.heading_color | default: 'rgba(var(--color-foreground), 1)' }};
    --footer-link-color: {{ section.settings.link_color | default: 'rgba(var(--color-foreground), 0.75)' }};
    --footer-border-color: {{ section.settings.border_color | default: 'rgba(var(--color-border), 0.2)' }};
    --newsletter-input-bg: {{ section.settings.newsletter_input_bg | default: '#ffffff' }};
    --newsletter-input-text-color: {{ section.settings.newsletter_input_text_color | default: '#333333' }};
    --newsletter-placeholder-color: {{ section.settings.newsletter_placeholder_color | default: '#999999' }};
    --newsletter-button-bg: {{ section.settings.newsletter_button_bg | default: '#333333' }};
    --newsletter-button-text-color: {{ section.settings.newsletter_button_text_color | default: '#ffffff' }};
    --newsletter-button-hover-bg: {{ section.settings.newsletter_button_hover_bg | default: '#555555' }};
    --newsletter-border-color: {{ section.settings.newsletter_border_color | default: 'rgba(var(--color-border), 0.2)' }};
    --newsletter-border-radius: {{ section.settings.newsletter_border_radius | default: 2 }}px;
    --newsletter-input-height: {{ section.settings.newsletter_input_height | default: 40 }}px;
    --newsletter-input-font-size: {{ section.settings.newsletter_input_font_size | default: 0.875 }}rem;
    --newsletter-button-font-size: {{ section.settings.newsletter_button_font_size | default: 0.875 }}rem;
    --newsletter-button-min-width: {{ section.settings.newsletter_button_min_width | default: 100 }}px;
    background: var(--footer-bg-color);
  "
>
  <!-- Main Footer Content -->
  <div class="footer-upstep__main">
    <!-- Logo Section -->
    {% if section.settings.show_logo %}
      <div class="footer-upstep__logo">
        {% if section.settings.logo != blank %}
          <img src="{{ section.settings.logo | image_url: width: 200 }}" alt="{{ shop.name }}" width="200" height="auto" loading="lazy">
        {% elsif settings.logo != blank %}
          <img src="{{ settings.logo | image_url: width: 200 }}" alt="{{ shop.name }}" width="200" height="auto" loading="lazy">
        {% else %}
          <h2 class="footer-upstep__logo-text">{{ shop.name }}</h2>
        {% endif %}
      </div>
    {% endif %}

    <!-- Menu Columns -->
    <div class="footer-upstep__menus">
      {% for block in section.blocks %}
        {% if block.type == 'menu_column' %}
          <div class="footer-upstep__menu-column" {{ block.shopify_attributes }}>
            {% if block.settings.heading != blank %}
              <h3 class="footer-upstep__menu-heading">{{ block.settings.heading }}</h3>
            {% endif %}
            
            {% if block.settings.menu != blank %}
              <ul class="footer-upstep__menu-list">
                {% for link in linklists[block.settings.menu].links %}
                  <li class="footer-upstep__menu-item">
                    <a href="{{ link.url }}" class="footer-upstep__menu-link"
                       {% if block.settings.open_in_new_tab %}target="_blank"{% endif %}>
                      {{ link.title }}
                    </a>
                  </li>
                {% endfor %}
              </ul>
            {% endif %}
          </div>
        {% endif %}
      {% endfor %}
    </div>

    <!-- Newsletter Section -->
    {% if section.settings.show_newsletter %}
      <div class="footer-upstep__newsletter">
        <div class="footer-upstep__newsletter-content">
          {% if section.settings.newsletter_heading != blank %}
            <h3 class="footer-upstep__newsletter-heading">{{ section.settings.newsletter_heading }}</h3>
          {% endif %}

          {% if section.settings.newsletter_text != blank %}
            <p class="footer-upstep__newsletter-text">{{ section.settings.newsletter_text }}</p>
          {% endif %}
        </div>

        <form action="{{ routes.root_url }}contact#contact_form" method="post" class="footer-upstep__newsletter-form" accept-charset="UTF-8">
          <input type="hidden" name="form_type" value="customer">
          <input type="hidden" name="utf8" value="✓">
          <input type="hidden" name="contact[tags]" value="newsletter">

          <div class="footer-upstep__newsletter-input-wrapper">
            <input
              type="email"
              name="contact[email]"
              class="footer-upstep__newsletter-input"
              placeholder="{{ section.settings.newsletter_placeholder | default: 'Email' }}"
              required
            >
            <button type="submit" class="footer-upstep__newsletter-button">
              {{ section.settings.newsletter_button_text | default: 'SUBMIT' }}
            </button>
          </div>
        </form>
      </div>
    {% endif %}
  </div>

  <!-- Footer Bottom -->
  <div class="footer-upstep__bottom">
    <!-- Copyright -->
    <div class="footer-upstep__copyright">
      <span>© {{ 'now' | date: '%Y' }} {{ section.settings.copyright_text | default: shop.name }}. {{ section.settings.copyright_suffix | default: 'All Rights Reserved.' }}</span>
      
      <!-- Policy Links -->
      {% if section.settings.show_policy_links %}
        <div class="footer-upstep__policy-links">
          {% if section.settings.privacy_policy_link != blank %}
            <a href="{{ section.settings.privacy_policy_link }}" class="footer-upstep__policy-link">Privacy Policy</a>
          {% endif %}
          {% if section.settings.terms_conditions_link != blank %}
            <a href="{{ section.settings.terms_conditions_link }}" class="footer-upstep__policy-link">Terms & Conditions</a>
          {% endif %}
          {% if section.settings.accessibility_link != blank %}
            <a href="{{ section.settings.accessibility_link }}" class="footer-upstep__policy-link">Accessibility</a>
          {% endif %}
        </div>
      {% endif %}
    </div>

    <!-- Payment Icons -->
    {% if section.settings.show_payment_icons %}
      <div class="footer-upstep__payment-icons">
        {% for block in section.blocks %}
          {% if block.type == 'payment_icon' %}
            <div class="footer-upstep__payment-icon" {{ block.shopify_attributes }}>
              {% if block.settings.icon != blank %}
                <img src="{{ block.settings.icon | image_url: width: 60 }}" alt="{{ block.settings.alt_text }}" loading="lazy">
              {% elsif block.settings.icon_type != blank %}
                <div class="footer-upstep__payment-icon-placeholder">{{ block.settings.icon_type }}</div>
              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </div>
    {% endif %}
  </div>
</footer>

{% stylesheet %}
  .footer-upstep {
    color: var(--footer-text-color);
  }

  .footer-upstep__main {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 2rem;
    align-items: start;
    margin-bottom: 1.5rem;
  }

  .footer-upstep__logo img {
    max-width: 80px;
    height: auto;
  }

  .footer-upstep__logo-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--footer-heading-color);
    margin: 0;
  }

  .footer-upstep__menus {
    display: grid;
    grid-template-columns: repeat(3, minmax(120px, 1fr));
    gap: 1.5rem;
    justify-content: center;
  }

  .footer-upstep__menu-heading {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--footer-heading-color);
    margin-bottom: 0.75rem;
  }

  .footer-upstep__menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .footer-upstep__menu-item {
    margin-bottom: 0.375rem;
  }

  .footer-upstep__menu-link {
    color: var(--footer-link-color);
    text-decoration: none;
    font-size: 0.8125rem;
    transition: color 0.3s ease;
  }

  .footer-upstep__menu-link:hover {
    color: var(--footer-text-color);
  }

  .footer-upstep__newsletter {
    max-width: 280px;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .footer-upstep__newsletter-content {
    text-align: left;
  }

  .footer-upstep__newsletter-heading {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--footer-heading-color);
    margin-bottom: 0.25rem;
    line-height: 1.3;
  }

  .footer-upstep__newsletter-text {
    font-size: 0.8125rem;
    color: var(--footer-link-color);
    margin: 0;
    line-height: 1.3;
  }

  .footer-upstep__newsletter-input-wrapper {
    display: flex;
    border: 1px solid var(--newsletter-border-color, var(--footer-border-color));
    border-radius: var(--newsletter-border-radius, 2px);
    overflow: hidden;
    height: var(--newsletter-input-height, 40px);
    background: var(--newsletter-input-bg, #ffffff);
  }

  .footer-upstep__newsletter-input {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: none;
    background: transparent;
    color: var(--newsletter-input-text-color, #333333);
    font-size: var(--newsletter-input-font-size, 0.875rem);
  }

  .footer-upstep__newsletter-input::placeholder {
    color: var(--newsletter-placeholder-color, #999999);
  }

  .footer-upstep__newsletter-input:focus {
    outline: 2px solid var(--newsletter-button-bg, #333333);
    outline-offset: -2px;
  }

  .footer-upstep__newsletter-button {
    padding: 0.5rem 1.5rem;
    background: var(--newsletter-button-bg, #333333);
    color: var(--newsletter-button-text-color, #ffffff);
    border: none;
    font-size: var(--newsletter-button-font-size, 0.875rem);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: var(--newsletter-button-min-width, 100px);
  }

  .footer-upstep__newsletter-button:hover {
    background: var(--newsletter-button-hover-bg, #555555);
    transform: translateY(-1px);
  }

  .footer-upstep__newsletter-button:focus {
    outline: 2px solid var(--newsletter-button-bg, #333333);
    outline-offset: 2px;
  }

  .footer-upstep__newsletter-button:active {
    transform: translateY(0);
    background: var(--newsletter-button-hover-bg, #555555);
  }

  .footer-upstep__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--footer-border-color);
    flex-wrap: wrap;
    gap: 1rem;
  }

  .footer-upstep__copyright {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    font-size: 0.8125rem;
    color: var(--footer-link-color);
  }

  .footer-upstep__policy-links {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .footer-upstep__policy-link {
    color: var(--footer-link-color);
    text-decoration: none;
    font-size: 0.8125rem;
    transition: color 0.3s ease;
  }

  .footer-upstep__policy-link:hover {
    color: var(--footer-text-color);
  }

  .footer-upstep__policy-link:not(:last-child)::after {
    content: " | ";
    margin-left: 1rem;
    color: var(--footer-border-color);
  }

  .footer-upstep__payment-icons {
    display: flex;
    gap: 0.375rem;
    align-items: center;
    flex-wrap: wrap;
  }

  .footer-upstep__payment-icon img {
    height: 20px;
    width: auto;
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }

  .footer-upstep__payment-icon img:hover {
    opacity: 1;
  }

  .footer-upstep__payment-icon-placeholder {
    height: 20px;
    padding: 3px 6px;
    background: var(--footer-border-color);
    color: var(--footer-link-color);
    font-size: 0.6875rem;
    font-weight: 600;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }

  .footer-upstep__payment-icon-placeholder:hover {
    opacity: 1;
  }

  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .footer-upstep__main {
      grid-template-columns: 1fr;
      gap: 2rem;
      text-align: center;
    }

    .footer-upstep__menus {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }

    .footer-upstep__newsletter {
      max-width: 100%;
      text-align: center;
    }

    .footer-upstep__newsletter-content {
      text-align: center;
    }

    .footer-upstep__bottom {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .footer-upstep__copyright {
      align-items: center;
    }

    .footer-upstep__policy-links {
      justify-content: center;
    }

    .footer-upstep__payment-icons {
      justify-content: center;
    }

    .footer-upstep__newsletter-input-wrapper {
      height: var(--newsletter-input-height, 44px);
    }

    .footer-upstep__newsletter-button {
      padding: 0.75rem 1rem;
      min-width: var(--newsletter-button-min-width, 80px);
    }
  }

  @media screen and (max-width: 480px) {
    .footer-upstep__menus {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .footer-upstep__newsletter-input-wrapper {
      flex-direction: column;
      height: auto;
      border-radius: var(--newsletter-border-radius, 4px);
    }

    .footer-upstep__newsletter-input {
      border-bottom: 1px solid var(--newsletter-border-color, rgba(var(--color-border), 0.2));
      padding: 0.75rem;
      font-size: var(--newsletter-input-font-size, 1rem);
    }

    .footer-upstep__newsletter-button {
      border-radius: 0;
      padding: 0.75rem;
      font-size: var(--newsletter-button-font-size, 1rem);
      min-width: auto;
    }

    .footer-upstep__policy-links {
      flex-direction: column;
      gap: 0.5rem;
    }

    .footer-upstep__policy-link:not(:last-child)::after {
      display: none;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Footer Upstep",
  "tag": "footer",
  "class": "footer-upstep-wrapper section-wrapper",
  "settings": [
    {
      "type": "header",
      "content": "Logo"
    },
    {
      "type": "checkbox",
      "id": "show_logo",
      "label": "Show logo",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Footer logo image",
      "info": "If not set, will use the main logo from theme settings"
    },
    {
      "type": "header",
      "content": "Newsletter"
    },
    {
      "type": "checkbox",
      "id": "show_newsletter",
      "label": "Show newsletter signup",
      "default": true
    },
    {
      "type": "text",
      "id": "newsletter_heading",
      "label": "Newsletter heading",
      "default": "Step with us! and get all our offers and benefits straight to your email!"
    },
    {
      "type": "textarea",
      "id": "newsletter_text",
      "label": "Newsletter description"
    },
    {
      "type": "text",
      "id": "newsletter_placeholder",
      "label": "Email placeholder",
      "default": "Email"
    },
    {
      "type": "text",
      "id": "newsletter_button_text",
      "label": "Button text",
      "default": "SUBMIT"
    },
    {
      "type": "header",
      "content": "Newsletter Styling"
    },
    {
      "type": "color",
      "id": "newsletter_input_bg",
      "label": "Input background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "newsletter_input_text_color",
      "label": "Input text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "newsletter_placeholder_color",
      "label": "Placeholder text color",
      "default": "#999999"
    },
    {
      "type": "color",
      "id": "newsletter_button_bg",
      "label": "Button background color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "newsletter_button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "newsletter_button_hover_bg",
      "label": "Button hover background color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "newsletter_border_color",
      "label": "Border color",
      "default": "#e0e0e0"
    },
    {
      "type": "range",
      "id": "newsletter_border_radius",
      "label": "Border radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 2
    },
    {
      "type": "range",
      "id": "newsletter_input_height",
      "label": "Input height",
      "min": 30,
      "max": 60,
      "step": 2,
      "unit": "px",
      "default": 40
    },
    {
      "type": "range",
      "id": "newsletter_input_font_size",
      "label": "Input font size",
      "min": 0.75,
      "max": 1.25,
      "step": 0.1,
      "unit": "rem",
      "default": 0.875
    },
    {
      "type": "range",
      "id": "newsletter_button_font_size",
      "label": "Button font size",
      "min": 0.75,
      "max": 1.25,
      "step": 0.1,
      "unit": "rem",
      "default": 0.875
    },
    {
      "type": "range",
      "id": "newsletter_button_min_width",
      "label": "Button minimum width",
      "min": 80,
      "max": 150,
      "step": 10,
      "unit": "px",
      "default": 100
    },
    {
      "type": "header",
      "content": "Copyright & Links"
    },
    {
      "type": "text",
      "id": "copyright_text",
      "label": "Copyright text",
      "default": "Upstep"
    },
    {
      "type": "text",
      "id": "copyright_suffix",
      "label": "Copyright suffix",
      "default": "All Rights Reserved."
    },
    {
      "type": "checkbox",
      "id": "show_policy_links",
      "label": "Show policy links",
      "default": true
    },
    {
      "type": "url",
      "id": "privacy_policy_link",
      "label": "Privacy Policy link"
    },
    {
      "type": "url",
      "id": "terms_conditions_link",
      "label": "Terms & Conditions link"
    },
    {
      "type": "url",
      "id": "accessibility_link",
      "label": "Accessibility link"
    },
    {
      "type": "header",
      "content": "Payment Icons"
    },
    {
      "type": "checkbox",
      "id": "show_payment_icons",
      "label": "Show payment icons",
      "default": true
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border color"
    },
    {
      "type": "header",
      "content": "Section settings"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "Section width",
      "options": [
        {
          "value": "page-width",
          "label": "Page width"
        },
        {
          "value": "full-width",
          "label": "Full width"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "Color scheme",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "1",
          "label": "Scheme 1"
        },
        {
          "value": "2",
          "label": "Scheme 2"
        }
      ],
      "default": ""
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "Top padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 48
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "Bottom padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 32
    }
  ],
  "blocks": [
    {
      "type": "menu_column",
      "name": "Menu Column",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Column heading",
          "default": "Company"
        },
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu"
        },
        {
          "type": "checkbox",
          "id": "open_in_new_tab",
          "label": "Open links in new tab",
          "default": false
        }
      ]
    },
    {
      "type": "payment_icon",
      "name": "Payment Icon",
      "settings": [
        {
          "type": "image_picker",
          "id": "icon",
          "label": "Payment icon"
        },
        {
          "type": "text",
          "id": "icon_type",
          "label": "Icon type (if no image)",
          "default": "VISA",
          "info": "Text to display if no image is uploaded"
        },
        {
          "type": "text",
          "id": "alt_text",
          "label": "Alt text",
          "default": "Payment method"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Footer Upstep",
      "blocks": [
        {
          "type": "menu_column",
          "settings": {
            "heading": "Company",
            "menu": ""
          }
        },
        {
          "type": "menu_column",
          "settings": {
            "heading": "Education",
            "menu": ""
          }
        },
        {
          "type": "menu_column",
          "settings": {
            "heading": "Support",
            "menu": ""
          }
        }
      ]
    }
  ]
}
{% endschema %}
