{"type": "footer", "name": "Footer Upstep", "sections": {"footer_upstep": {"type": "footer-upstep", "blocks": {"menu_company": {"type": "menu_column", "settings": {"heading": "Company", "menu": "main-menu", "open_in_new_tab": false}}, "menu_education": {"type": "menu_column", "settings": {"heading": "Education", "menu": "main-menu", "open_in_new_tab": false}}, "menu_support": {"type": "menu_column", "settings": {"heading": "Support", "menu": "main-menu", "open_in_new_tab": false}}, "payment_visa": {"type": "payment_icon", "settings": {"icon_type": "VISA", "alt_text": "Visa"}}, "payment_mastercard": {"type": "payment_icon", "settings": {"icon_type": "MC", "alt_text": "Mastercard"}}, "payment_amex": {"type": "payment_icon", "settings": {"icon_type": "AMEX", "alt_text": "American Express"}}, "payment_paypal": {"type": "payment_icon", "settings": {"icon_type": "PAYPAL", "alt_text": "PayPal"}}, "payment_gpay": {"type": "payment_icon", "settings": {"icon_type": "GPAY", "alt_text": "Google Pay"}}, "payment_apple": {"type": "payment_icon", "settings": {"icon_type": "APPLE", "alt_text": "Apple Pay"}}, "payment_discover": {"type": "payment_icon", "settings": {"icon_type": "DISC", "alt_text": "Discover"}}, "payment_fsa": {"type": "payment_icon", "settings": {"icon_type": "FSA", "alt_text": "FSA"}}}, "block_order": ["menu_company", "menu_education", "menu_support", "payment_visa", "payment_mastercard", "payment_amex", "payment_paypal", "payment_gpay", "payment_apple", "payment_discover", "payment_fsa"], "settings": {"show_logo": true, "show_newsletter": true, "newsletter_heading": "Step with us! and get all our offers and benefits straight to your email!", "newsletter_text": "", "newsletter_placeholder": "Email", "newsletter_button_text": "SUBMIT", "copyright_text": "Upstep", "copyright_suffix": "All Rights Reserved.", "show_policy_links": true, "privacy_policy_link": "", "terms_conditions_link": "", "accessibility_link": "", "show_payment_icons": true, "background_color": "#e6e6e6", "text_color": "", "heading_color": "", "link_color": "", "border_color": "", "section_width": "page-width", "color_scheme": "", "padding-block-start": 32, "padding-block-end": 24}}}, "order": ["footer_upstep"]}